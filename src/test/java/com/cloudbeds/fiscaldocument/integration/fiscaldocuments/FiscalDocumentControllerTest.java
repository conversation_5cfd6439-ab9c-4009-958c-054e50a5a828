package com.cloudbeds.fiscaldocument.integration.fiscaldocuments;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.accounting.v1.InternalCode;
import com.cloudbeds.accounting.v1.InternalCodeGroup;
import com.cloudbeds.accounting.v1.ListPostedTransactionsWithFolioResponse;
import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.accounting.v1.Transaction;
import com.cloudbeds.accounting.v1.TransactionWithFolio;
import com.cloudbeds.currency.v1.CurrencyRateType;
import com.cloudbeds.currency.v1.PropertyActiveCurrencyRate;
import com.cloudbeds.currency.v1.PropertyCurrency;
import com.cloudbeds.currency.v1.PropertyCurrencyFormat;
import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.controller.model.CreationMethod;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentEmailRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionsPaginated;
import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.PropertyCurrencyServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.FiscalDocumentPreconditions;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import com.cloudbeds.fiscaldocument.services.BookingService;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.services.DocumentSequenceService;
import com.cloudbeds.fiscaldocument.services.EmailService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.cloudbeds.fiscaldocument.services.S3Service;
import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.cloudbeds.fiscaldocument.utils.DateTimeService;
import com.cloudbeds.guest.v1.ContactDetails;
import com.cloudbeds.guest.v1.PersonVersion;
import com.cloudbeds.organization.v1.Property;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.protobuf.Timestamp;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;

import static com.cloudbeds.fiscaldocument.converters.SourceKindConverter.convert;
import static com.cloudbeds.fiscaldocument.enums.SourceKind.GROUP_PROFILE;
import static com.cloudbeds.fiscaldocument.enums.SourceKind.RESERVATION;
import static com.cloudbeds.fiscaldocument.integration.utils.KafkaUtils.consumeMessages;
import static com.cloudbeds.fiscaldocument.integration.utils.KafkaUtils.subscribeConsumers;
import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getObjectFromJsonResource;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;

@Slf4j
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class},
    properties = {
        "consumer.fiscal_document_events.enabled=true",
        "topics.fiscal_document_events=topics.fiscal_document_events_FiscalDocumentControllerTest",
        "topics.fiscal_documents=topics.fiscal_documents_FiscalDocumentControllerTest",
        "listeners.fiscal_document_events.group_id=fiscal_document_events_FiscalDocumentControllerTest",
        "sync.documents.initial-delay-ms=100"
    }
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FiscalDocumentControllerTest extends BaseIntegrationTest {
    private static final KafkaConsumer<FiscalDocumentEventKey, FiscalDocumentEventValue> EVENTS_CONSUMER =
        getFiscalDocumentEventsConsumer();

    private static final KafkaConsumer<FiscalDocumentKey, FiscalDocumentValue> DOCUMENTS_CONSUMER =
        getFiscalDocumentsConsumer();

    @Value(value = "${topics.fiscal_document_events}")
    private String eventsTopic;

    @Value(value = "${topics.fiscal_documents}")
    private String documentsTopic;

    @MockBean
    private AccountingService accountingService;
    @MockBean
    private PropertyServiceClient propertyServiceClient;
    @MockBean
    private PropertyCurrencyServiceClient propertyCurrencyServiceClient;
    @MockBean
    private S3Service s3Service;
    @MockBean
    private EmailService emailService;
    @Autowired
    private DocumentSequenceService documentSequenceService;
    @Autowired
    private DocumentConfigService documentConfigService;
    @Autowired
    private FiscalDocumentPreconditions fiscalDocumentPreconditions;

    @Autowired
    private FiscalDocumentRepository repository;
    @MockBean
    private BookingService bookingService;

    @MockBean
    private GroupProfileServiceClient groupProfileServiceClient;

    @MockBean
    private DateTimeService dateTimeService;

    @MockBean
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;
    @Autowired
    private FiscalDocumentRepository fiscalDocumentRepository;

    private void setupDataAndMocks(Property property) {
        doReturn(property).when(propertyServiceClient).getProperty(any());
        doReturn(List.of(property)).when(propertyServiceClient).listProperties(
            List.of(property.getId())
        );
        doReturn(true).when(propertyServiceClient).isPropertyFeatureEnabled(
            any(), any()
        );

        doAnswer((answer -> {
            File file = answer.getArgument(0);
            log.info(file.getAbsolutePath());
            return null;
        })).when(s3Service).uploadContent(any(), any(), any());

        doReturn(List.of(
            Transaction.newBuilder()
                .setId(1L)
                .setSource(Source.SOURCE_RESERVATION)
                .setSourceId(1)
                .setInternalCode(
                    InternalCode.newBuilder()
                        .setCode("1000")
                        .setGroupValue(InternalCodeGroup.INTERNAL_CODE_GROUP_ROOM_REVENUE_RATE.getNumber()))
                .setAmount(10000)
                .setCurrencyScale(2)
                .setCurrency("USD")
                .buildPartial(),
            Transaction.newBuilder()
                .setId(2L)
                .setSource(Source.SOURCE_RESERVATION)
                .setSourceId(1)
                .setInternalCode(
                    InternalCode.newBuilder()
                        .setCode("8000")
                        .setGroupValue(InternalCodeGroup.INTERNAL_CODE_GROUP_TAX.getNumber()))
                .setAmount(1000)
                .setCurrencyScale(2)
                .setCurrency("USD")
                .buildPartial()
        ))
            .when(accountingService)
            .getPostedTransactions(
                eq(property.getId()),
                eq(List.of(1L, 2L)),
                eq(1L),
                eq(RESERVATION));
        doReturn(List.of())
            .when(fiscalDocumentRecipientService)
            .getRecipients(
                anyLong(),
                anyLong(),
                anyList(),
                any(com.cloudbeds.fiscaldocument.enums.SourceKind.class),
                anyLong()
            );

        doReturn(DEFAULT_RESERVATION).when(bookingService).getBooking(any(), any());
        doReturn(List.of()).when(bookingService).getBookings(any(), any());
        doReturn(property).when(propertyServiceClient).getProperty(any());
        doReturn(List.of(DEFAULT_GROUP_PROFILE)).when(groupProfileServiceClient).listGroups(any(), any());
        doReturn(LocalDate.of(2025, 5, 23)).when(dateTimeService).getLocalDate(any());


        doReturn(Map.of("MQ==", List.of(1L), "Mg==", List.of(2L)))
            .when(accountingService).getTransactionIdsFromMfdTransactions(
                List.of("MQ==", "Mg==")
            );

        doReturn(
            List.of(
                PropertyCurrency.newBuilder()
                    .setCurrencyCode("USD")
                    .setIsActive(true)
                    .setRateType(CurrencyRateType.CURRENCY_RATE_TYPE_AUTO)
                    .build(),
                PropertyCurrency.newBuilder()
                    .setCurrencyCode("EUR")
                    .setIsActive(true)
                    .setDisplayInvoiceTotal(true)
                    .setDisplayInvoiceRate(true)
                    .setRateType(CurrencyRateType.CURRENCY_RATE_TYPE_MANUAL)
                    .build()
            ))
            .when(propertyCurrencyServiceClient).listPropertyCurrencies(eq(property.getId()));

        doReturn(
            List.of(
                PropertyActiveCurrencyRate.newBuilder()
                    .setCurrencyCode("USD")
                    .setRate(1.0)
                    .build(),
                PropertyActiveCurrencyRate.newBuilder()
                    .setCurrencyCode("EUR")
                    .setRate(1.135)
                    .build()
            )
        )
            .when(propertyCurrencyServiceClient).listPropertyActiveCurrencyRatesByPropertyId(anyLong());

        doReturn(
            PropertyCurrencyFormat.newBuilder()
                .setDecimalSeparator(".")
                .setThousandSeparator(",")
                .build()
        )
            .when(propertyCurrencyServiceClient).getPropertyCurrencyFormat(anyLong());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("listFiscalDocumentsArguments")
    void listFiscalDocumentsTest(
        String name,
        Map<String, Object> queryParams,
        HttpStatus expectedStatus,
        com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated expectedResponse
    ) {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();

        var response = getRequest(
            "/fiscal-documents",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated>() {},
            queryParams
        );

        assertEquals(expectedStatus, response.getStatusCode());

        if (expectedStatus == HttpStatus.OK) {
            assertEquals(expectedResponse.getFiscalDocuments().size(), response.getBody().getFiscalDocuments().size());
            for (int i = 0; i < expectedResponse.getFiscalDocuments().size(); i++) {
                var expected = expectedResponse.getFiscalDocuments().get(i);
                var actual = response.getBody().getFiscalDocuments().get(i);
                assertEquals(expected.getId(), actual.getId());
                assertEquals(expected.getNumber(), actual.getNumber());
                assertEquals(expected.getStatus(), actual.getStatus());
                assertEquals(expected.getKind(), actual.getKind());
                assertEquals(expected.getPropertyId(), actual.getPropertyId());
                assertEquals(expected.getSourceId(), actual.getSourceId());
                assertEquals(expected.getSourceKind(), actual.getSourceKind());
                assertEquals(expected.getUserId(), actual.getUserId());
                assertEquals(expected.getInvoiceDate(), actual.getInvoiceDate());
                assertEquals(expected.getExternalId(), actual.getExternalId());
                assertEquals(expected.getOrigin(), actual.getOrigin());
                assertEquals(expected.getAmount(), actual.getAmount());
                assertEquals(expected.getBalance(), actual.getBalance());
                assertEquals(expected.getDueDate(), actual.getDueDate());
                assertEquals(expected.getUserId(), actual.getUserId());
                assertEquals(expected.getActions(), actual.getActions());
                assertEquals(expected.getGovernmentIntegration(), actual.getGovernmentIntegration());
                assertEquals(expected.getRecipients(), actual.getRecipients());
                assertEquals(expected.getParentId(), actual.getParentId());
            }
        }
    }

    private static Stream<Arguments> listFiscalDocumentsArguments() throws IOException {
        return Stream.of(
            org.junit.jupiter.params.provider.Arguments.of(
                "Get all documents",
                Map.of(),
                HttpStatus.OK,
                getObjectFromJsonResource(
                    "integration/list-documents/get-all/response.json",
                    com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated.class
                )
            ),
            org.junit.jupiter.params.provider.Arguments.of(
                "Get for source id = 1 and source kind = GROUP_PROFILE",
                Map.of("sourceIds", "1", "sourceKind", "GROUP_PROFILE"),
                HttpStatus.OK,
                getObjectFromJsonResource(
                    "integration/list-documents/source-filter/response.json",
                    com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated.class
                )
            ),
            org.junit.jupiter.params.provider.Arguments.of(
                "Get by ids",
                Map.of("ids", "1,2"),
                HttpStatus.OK,
                getObjectFromJsonResource(
                    "integration/list-documents/ids-filter/response.json",
                    com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated.class
                )
            ),
            org.junit.jupiter.params.provider.Arguments.of(
                "Get with invalid status",
                Map.of("statuses", "INVALID_STATUS"),
                HttpStatus.BAD_REQUEST,
                null
            ),
            org.junit.jupiter.params.provider.Arguments.of(
                "Get by number",
                Map.of("numberContains", "2-"),
                HttpStatus.OK,
                getObjectFromJsonResource(
                    "integration/list-documents/get-by-number/response.json",
                    com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated.class
                )
            )
        );
    }

    @Test
    void testServiceDisabledInApiKeyResponse() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        Mockito.reset(providerInterface);
        doReturn(false)
            .when(providerInterface)
            .evaluatePropertyFlag(any(), eq(FeatureFlags.FISCAL_DOCUMENT_SERVICE_ENABLED));

        fiscalDocumentPreconditions.createInvoices();

        var response = getRequest(
            "/fiscal-documents",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<String>() {},
            Map.of()
        );

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertTrue(response.getBody().contains("Invoicing is disabled with feature flag for property"));
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("createFiscalDocumentSource")
    void createFiscalDocumentTest(
        String testName,
        Property property,
        boolean isIntegrationEnabled,
        String eventKeyPath,
        String eventValuePath,
        String expectedDocumentsPath
    ) throws IOException, InterruptedException {
        subscribeConsumers(
            List.of(
                Pair.of(EVENTS_CONSUMER, eventsTopic),
                Pair.of(DOCUMENTS_CONSUMER, documentsTopic)
            ),
            300
        );

        setupDataAndMocks(property);
        Mockito.reset(marketplaceServiceClient);
        doReturn(isIntegrationEnabled)
            .when(marketplaceServiceClient)
            .getInvoiceIntegrationEnabled(any());

        var config = documentConfigService.createConfig(
            property.getId(), DocumentKind.INVOICE, true, null
        );
        config.setDueDays(30);
        documentConfigService.saveAll(List.of(config));
        documentSequenceService.createDefaultSequence(property.getId());

        var nextId = new AtomicLong(1);
        doAnswer(invocation -> {
            System.out.println("generate ID from nextId");
            return nextId.getAndIncrement();
        }).when(idPool).nextIdWithRetry(anyInt(), anyLong());

        var request = new com.cloudbeds.fiscaldocument.controller.model.CreateInvoiceRequest();
        request.setSourceId(1L);
        request.setSourceKind(SourceKind.RESERVATION);
        request.setTransactionIds(List.of(1L, 2L));
        request.setUserId(0L);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, 1L)
        );
        var response = postRequest(
            "/fiscal-documents/invoice",
            property.getId(),
            request,
            Object.class
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());

        EVENTS_CONSUMER.subscribe(List.of(eventsTopic));
        var eventMessages = consumeMessages(EVENTS_CONSUMER, eventsTopic, 1, 5000);
        assertEquals(1, eventMessages.size());

        var expectedEventKey = getObjectFromJsonResource(
            eventKeyPath,
            FiscalDocumentEventKey.class
        );

        var expectedEventValue = getObjectFromJsonResource(
            eventValuePath,
            FiscalDocumentEventValue.class
        );
        assertEquals(expectedEventKey, eventMessages.get(0).key());
        assertEquals(expectedEventValue, eventMessages.get(0).value());

        DOCUMENTS_CONSUMER.subscribe(List.of(documentsTopic));
        var documentMessages = consumeMessages(DOCUMENTS_CONSUMER, documentsTopic, 2, 5000);
        assertEquals(2, documentMessages.size());

        List<FiscalDocumentValue> expectedDocumentValues = getObjectFromJsonResource(
            expectedDocumentsPath,
            new TypeReference<>() {}
        );

        for (int i = 0; i < documentMessages.size(); i++) {
            compareDocumentValue(
                expectedDocumentValues.get(i),
                documentMessages.get(i).value()
            );
        }
    }

    @Test
    void createFiscalDocumentExistingTransactionsTest() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        documentConfigService.createConfig(
            CB_PROPERTY_ID, DocumentKind.INVOICE, true, null
        );
        documentSequenceService.createDefaultSequence(CB_PROPERTY_ID);

        var request = new com.cloudbeds.fiscaldocument.controller.model.CreateInvoiceRequest();
        request.setSourceId(1L);
        request.setSourceKind(SourceKind.RESERVATION);
        request.setTransactionIds(List.of(1L, 2L));
        request.setUserId(0L);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, 1L)
        );

        postRequest(
            "/fiscal-documents/invoice",
            CB_PROPERTY_ID,
            request,
            Object.class
        );

        var response = postRequest(
            "/fiscal-documents/invoice",
            CB_PROPERTY_ID,
            request,
            com.cloudbeds.fiscaldocument.controller.model.ApiError.class
        );

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        assertTrue(response.getBody().getErrorDetails().contains("Transactions are already invoiced:"));
        assertEquals("INVALID_REQUEST", response.getBody().getErrorCode());
    }

    @Test
    void createCreditNoteDocumentTest() throws IOException, InterruptedException {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();
        subscribeConsumers(
            List.of(
                Pair.of(EVENTS_CONSUMER, eventsTopic),
                Pair.of(DOCUMENTS_CONSUMER, documentsTopic)
            ),
            300
        );

        documentConfigService.createConfig(
            CB_PROPERTY_ID, DocumentKind.CREDIT_NOTE, true, null
        );

        var nextId = new AtomicLong(10);
        doAnswer(invocation -> {
            System.out.println("generate ID from nextId");
            return nextId.getAndIncrement();
        }).when(idPool).nextIdWithRetry(anyInt(), anyLong());

        var request = new com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest();
        request.setInvoiceId(2L);
        request.setUserId(0L);
        request.setReason("reason");
        request.setMethod(CreationMethod.VOID);
        var response = postRequest(
            "/fiscal-documents/credit-note",
            CB_PROPERTY_ID,
            request,
            Object.class
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());

        EVENTS_CONSUMER.subscribe(List.of(eventsTopic));
        var eventMessages = consumeMessages(EVENTS_CONSUMER, eventsTopic, 3, 5000);
        assertEquals(1, eventMessages.size());

        var expectedKey = getObjectFromJsonResource(
            "integration/create-document/credit-note/event_key.json",
            FiscalDocumentEventKey.class
        );

        var expectedValue = getObjectFromJsonResource(
            "integration/create-document/credit-note/event_value.json",
            FiscalDocumentEventValue.class
        );
        assertEquals(expectedKey, eventMessages.get(0).key());
        assertEquals(expectedValue, eventMessages.get(0).value());

        DOCUMENTS_CONSUMER.subscribe(List.of(documentsTopic));
        var documentMessages = consumeMessages(DOCUMENTS_CONSUMER, documentsTopic, 3, 5000);
        assertEquals(3, documentMessages.size());

        List<FiscalDocumentValue> expectedDocumentValues = getObjectFromJsonResource(
            "integration/create-document/credit-note/document_value.json",
            new TypeReference<>() {}
        );

        for (int i = 0; i < documentMessages.size(); i++) {
            compareDocumentValue(
                expectedDocumentValues.get(i),
                documentMessages.get(i).value()
            );
        }
    }



    @Test
    void createRectifyInvoiceDocumentTest() throws IOException, InterruptedException {
        setupDataAndMocks(SPAIN_PROPERTY);
        doReturn(true)
            .when(marketplaceServiceClient)
            .getInvoiceIntegrationEnabled(any());
        fiscalDocumentPreconditions.createInvoices();
        subscribeConsumers(
            List.of(
                Pair.of(EVENTS_CONSUMER, eventsTopic),
                Pair.of(DOCUMENTS_CONSUMER, documentsTopic)
            ),
            300
        );

        var recipient = new Recipient();
        recipient.setId("1");
        recipient.setFirstName("John");
        recipient.setLastName("Doe");
        recipient.setEmail("<EMAIL>");
        recipient.setType(Recipient.RecipientType.PERSON);

        var fiscalDocumentRecipient = new FiscalDocumentRecipient();
        fiscalDocumentRecipient.setFiscalDocumentId(10L);
        fiscalDocumentRecipient.setRecipientId(1L);
        fiscalDocumentRecipient.setRecipient(recipient);

        doReturn(List.of(fiscalDocumentRecipient))
            .when(fiscalDocumentRecipientService)
            .getRecipients(
                anyLong(),
                anyLong(),
                anyList(),
                any(com.cloudbeds.fiscaldocument.enums.SourceKind.class),
                anyLong()
            );

        documentConfigService.createConfig(
            CB_PROPERTY_ID, DocumentKind.RECTIFY_INVOICE, true, null
        );

        var nextId = new AtomicLong(10);
        doAnswer(invocation -> {
            System.out.println("generate ID from nextId");
            return nextId.getAndIncrement();
        }).when(idPool).nextIdWithRetry(anyInt(), anyLong());

        var request = new com.cloudbeds.fiscaldocument.controller.model.RectifyInvoiceNoteRequest();
        request.setInvoiceId(2L);
        request.setUserId(0L);
        request.setReason("reason");
        request.setMethod(CreationMethod.VOID);
        var response = postRequest(
            "/fiscal-documents/rectify-invoice",
            CB_PROPERTY_ID,
            request,
            Object.class
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());

        DOCUMENTS_CONSUMER.subscribe(List.of(documentsTopic));
        var documentMessages = consumeMessages(DOCUMENTS_CONSUMER, documentsTopic, 3, 5000);
        assertEquals(2, documentMessages.size());

        List<FiscalDocumentValue> expectedDocumentValues = getObjectFromJsonResource(
            "integration/create-document/rectify-invoice/document_value.json",
            new TypeReference<>() {}
        );

        for (int i = 0; i < documentMessages.size(); i++) {
            compareDocumentValue(
                expectedDocumentValues.get(i),
                documentMessages.get(i).value()
            );
        }
    }

    @Test
    void createCreditNoteAndVoidCreditNoteTest() throws IOException, InterruptedException {
        var nextId = new AtomicLong(10);
        doAnswer(invocation -> {
            System.out.println("generate ID from nextId");
            return nextId.getAndIncrement();
        }).when(idPool).nextIdWithRetry(anyInt(), anyLong());

        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();

        documentConfigService.createConfig(
            CB_PROPERTY_ID,
            DocumentKind.CREDIT_NOTE,
            true,
            null);
        DOCUMENTS_CONSUMER.subscribe(List.of(documentsTopic));

        var request = new com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest();
        var openInvoiceId = 7L;
        request.setInvoiceId(openInvoiceId);
        request.setUserId(0L);
        request.setReason("reason adj");
        request.setMethod(CreationMethod.ADJUSTMENT);
        var response = postRequest(
            "/fiscal-documents/credit-note",
            CB_PROPERTY_ID,
            request,
            com.cloudbeds.fiscaldocument.controller.model.ApiError.class
        );
        assertEquals(HttpStatus.OK, response.getStatusCode(), response.getBody().getErrorDetails());

        var voidRequest = new com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest();
        voidRequest.setInvoiceId(openInvoiceId);
        voidRequest.setUserId(0L);
        voidRequest.setReason("reason void");
        voidRequest.setMethod(CreationMethod.VOID);
        var voidResponse = postRequest(
            "/fiscal-documents/credit-note",
            CB_PROPERTY_ID,
            voidRequest,
            com.cloudbeds.fiscaldocument.controller.model.ApiError.class
        );
        assertEquals(HttpStatus.OK, voidResponse.getStatusCode(), response.getBody().getErrorDetails());

        var documentMessages = consumeMessages(DOCUMENTS_CONSUMER, documentsTopic, 6, 5000);
        assertEquals(6, documentMessages.size());

        List<FiscalDocumentValue> expectedDocumentValues = getObjectFromJsonResource(
            "integration/create-document/credit-note-adj-void/document_value.json",
            new TypeReference<>() {}
        );

        for (int i = 0; i < documentMessages.size(); i++) {
            compareDocumentValue(
                expectedDocumentValues.get(i),
                documentMessages.get(i).value()
            );
        }
    }

    @Test
    void getAvailableTransactionsWhenAdjCreditNoteIsVoided() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createVoidedAdjustmentCreditNote();

        var response = getRequest(
            "/fiscal-documents/transactions",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<FiscalDocumentTransactionsPaginated>() {},
            Map.of("sourceId", "1", "sourceKind", "GROUP_PROFILE", "forDocumentType", "INVOICE")
        );

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        // no transactions should be sent to AS as filter
        Mockito.verify(accountingService, Mockito.times(1))
            .getAvailableTransactions(anyLong(), eq(new HashSet<>()), anyLong(), any(), any(), any(), any());
    }

    @MethodSource("putFiscalDocumentArguments")
    @ParameterizedTest(name = "{0}")
    void putFiscalDocumentTest(
        String name,
        com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration governmentIntegration,
        FiscalDocumentStatus status,
        HttpStatus expectedStatus,
        String failReason,
        String expectedEventKeyPath,
        String expectedEventValuePath
    ) throws IOException {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();
        subscribeConsumers(
            List.of(
                Pair.of(EVENTS_CONSUMER, eventsTopic),
                Pair.of(DOCUMENTS_CONSUMER, documentsTopic)
            ),
            300
        );

        var request = new com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPatchRequest();
        request.setStatus(status);
        request.setGovernmentIntegration(governmentIntegration);
        request.setFailReason(failReason);
        var response = putRequest(
            "/fiscal-documents/8000",
            CB_PROPERTY_ID,
            request,
            com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentSummaryResponse.class
        );

        assertEquals(expectedStatus, response.getStatusCode());

        if (expectedStatus.equals(HttpStatus.OK)) {
            assertEquals(
                status.getValue(),
                response.getBody().getStatus().getValue()
            );
            assertEquals(
                governmentIntegration.getSeries(),
                response.getBody().getGovernmentIntegration().getSeries()
            );
            assertEquals(
                governmentIntegration.getNumber(),
                response.getBody().getGovernmentIntegration().getNumber()
            );
            assertEquals(
                governmentIntegration.getStatus(),
                response.getBody().getGovernmentIntegration().getStatus()
            );
            assertEquals(
                governmentIntegration.getOfficialId(),
                response.getBody().getGovernmentIntegration().getOfficialId()
            );
            assertEquals(
                governmentIntegration.getExternalId(),
                response.getBody().getGovernmentIntegration().getExternalId()
            );
            assertEquals(
                governmentIntegration.getRectifyingInvoiceType(),
                response.getBody().getGovernmentIntegration().getRectifyingInvoiceType()
            );
            assertEquals(
                governmentIntegration.getUrl(),
                response.getBody().getGovernmentIntegration().getUrl()
            );
            assertEquals(
                governmentIntegration.getQr().getUrl(),
                response.getBody().getGovernmentIntegration().getQr().getUrl()
            );
            assertEquals(
                governmentIntegration.getQr().getString(),
                response.getBody().getGovernmentIntegration().getQr().getString()
            );
            var expectedCount = expectedEventKeyPath == null ? 0 : 1;

            EVENTS_CONSUMER.subscribe(List.of(eventsTopic));
            var eventMessages = consumeMessages(EVENTS_CONSUMER, eventsTopic, 1, 500);
            assertEquals(expectedCount, eventMessages.size());

            if (expectedEventKeyPath != null) {
                var expectedEventKey = getObjectFromJsonResource(
                    expectedEventKeyPath,
                    FiscalDocumentEventKey.class
                );

                var expectedEventValue = getObjectFromJsonResource(
                    expectedEventValuePath,
                    FiscalDocumentEventValue.class
                );
                assertEquals(expectedEventKey, eventMessages.get(0).key());
                assertEquals(expectedEventValue, eventMessages.get(0).value());
            }
        }

    }

    @Test
    void getFiscalDocumentTransactions() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();

        long customerId = 101L;
        var transactionWithFolio = TransactionWithFolio
            .newBuilder()
            .setTransaction(
                Transaction.newBuilder()
                .setCurrencyScale(2)
                .setAmount(10000)
                .setInternalCode(InternalCode.newBuilder()
                    .setCode("1000")
                    .build())
                .setId(2L)
                .setDescription("description")
                .setPropertyId(CB_PROPERTY_ID)
                .setCustomerId(customerId)
                .setSourceId(1L)
                .setSource(Source.SOURCE_GROUP_PROFILE)
                .setTransactionDatetime(Timestamp.newBuilder()
                    .setSeconds(**********)
                    .build())
            )
            .setFolioId(1L)
            .build();

        doReturn(ListPostedTransactionsWithFolioResponse.newBuilder()
                .addTransactions(transactionWithFolio)
                .setNextPageToken("1")
                .build())
            .when(accountingService)
            .getAvailableTransactions(
                eq(CB_PROPERTY_ID),
                eq(Set.of(1L)),
                eq(1L),
                eq(GROUP_PROFILE),
                eq(DocumentKind.INVOICE),
                eq(null),
                eq(20));

        var guestResponse = PersonVersion.newBuilder()
            .setId(112345L)
            .setContactDetails(
                ContactDetails.newBuilder()
                    .setFirstName("John")
                    .setLastName("Doe")
                    .build()
            ).build();
        doReturn(Map.of(customerId, guestResponse)).when(fiscalDocumentRecipientService)
            .getGuestInfo(eq(CB_PROPERTY_ID), anySet());

        var response = getRequest(
            "/fiscal-documents/transactions",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<FiscalDocumentTransactionsPaginated>() {},
            Map.of("sourceId", "1", "sourceKind", "GROUP_PROFILE", "forDocumentType", "INVOICE")
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getTransactions().size());


        var responseTransaction = response.getBody().getTransactions().get(0);
        validateFiscalDocumentTransactions(transactionWithFolio, responseTransaction);

    }

    private void validateFiscalDocumentTransactions(
        TransactionWithFolio expectedWithFolio,
        com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionResponse actual
    ) {

        var expected = expectedWithFolio.getTransaction();

        assertEquals(expectedWithFolio.getFolioId(), Long.parseLong(actual.getFolioId()));
        assertEquals(expected.getInternalCode().getCode(), actual.getInternalCode());
        assertEquals(expected.getDescription(), actual.getDescription());
        assertEquals(expected.getPropertyId(), Long.parseLong(actual.getPropertyId()));
        assertEquals(expected.getSourceId(), Long.parseLong(actual.getSourceId()));
        assertEquals(convert(expected.getSource()), actual.getSourceKind());
        assertEquals(expected.getId(), Long.parseLong(actual.getId()));
        assertEquals("John Doe", actual.getGuestName());
        assertEquals(
            expected.getTransactionDatetime().getSeconds(),
            actual.getTransactionDate().toInstant().getEpochSecond()
        );
        assertEquals(
            expected.getAmount() / Math.pow(10, expected.getCurrencyScale()),
            actual.getAmount().doubleValue()
        );
    }

    @Test
    void testGetTransactionsByFiscalDocumentId() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createVoidedAdjustmentCreditNote();

        long customerId = 101L;
        var transactionWithFolio = TransactionWithFolio
            .newBuilder()
            .setTransaction(
                Transaction.newBuilder()
                    .setCurrencyScale(2)
                    .setAmount(10000)
                    .setInternalCode(InternalCode.newBuilder()
                        .setCode("1000")
                        .build())
                    .setId(2L)
                    .setDescription("description")
                    .setPropertyId(CB_PROPERTY_ID)
                    .setSourceId(1L)
                    .setCustomerId(customerId)
                    .setSource(Source.SOURCE_GROUP_PROFILE)
                    .setTransactionDatetime(Timestamp.newBuilder()
                        .setSeconds(**********)
                        .build())
            )
            .setFolioId(1L)
            .build();

        doReturn(ListPostedTransactionsWithFolioResponse.newBuilder()
            .addTransactions(transactionWithFolio)
            .build())
            .when(accountingService)
            .getTransactionsByIds(
                eq(CB_PROPERTY_ID),
                eq(List.of(2L)),
                any(),
                eq(20)
            );


        var guestResponse = PersonVersion.newBuilder()
            .setId(134562356L)
            .setContactDetails(
                ContactDetails.newBuilder()
                    .setFirstName("John")
                    .setLastName("Doe")
                    .build()
            ).build();
        doReturn(Map.of(customerId, guestResponse)).when(fiscalDocumentRecipientService)
            .getGuestInfo(eq(CB_PROPERTY_ID), anySet());


        var response = getRequest(
            "/fiscal-documents/" + 2 + "/transactions",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<FiscalDocumentTransactionsPaginated>() {},
            null
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getTransactions().size());

        var responseTransaction = response.getBody().getTransactions().get(0);
        this.validateFiscalDocumentTransactions(transactionWithFolio, responseTransaction);

        var emptyResponse = getRequest(
            "/fiscal-documents/" + 1 + "/transactions",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<FiscalDocumentTransactionsPaginated>() {},
            Map.of("limit", "50")
        );

        assertEquals(HttpStatus.OK, emptyResponse.getStatusCode());
        assertEquals(0, emptyResponse.getBody().getTransactions().size());
    }


    @Test
    void testGetTransactionsByFiscalDocumentIdForVoidCreditNote() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createVoidedAdjustmentCreditNote();

        var transactionWithFolio1 = TransactionWithFolio
            .newBuilder()
            .setTransaction(
                Transaction.newBuilder()
                    .setCurrencyScale(2)
                    .setAmount(10000)
                    .setInternalCode(InternalCode.newBuilder()
                        .setCode("1000")
                        .build())
                    .setId(1L)
                    .setDescription("description")
                    .setPropertyId(CB_PROPERTY_ID)
                    .setSourceId(1L)
                    .setCustomerId(1L)
                    .setSource(Source.SOURCE_GROUP_PROFILE)
                    .setTransactionDatetime(Timestamp.newBuilder()
                        .setSeconds(**********)
                        .build())
            )
            .setFolioId(1L)
            .build();

        long customerId = 1L;
        var transactionWithFolio2 = TransactionWithFolio
            .newBuilder()
            .setTransaction(
                Transaction.newBuilder()
                    .setCurrencyScale(2)
                    .setAmount(10000)
                    .setInternalCode(InternalCode.newBuilder()
                        .setCode("1000")
                        .build())
                    .setId(2L)
                    .setDescription("description")
                    .setPropertyId(CB_PROPERTY_ID)
                    .setSourceId(1L)
                    .setCustomerId(customerId)
                    .setSource(Source.SOURCE_GROUP_PROFILE)
                    .setTransactionDatetime(Timestamp.newBuilder()
                        .setSeconds(**********)
                        .build())
            )
            .setFolioId(1L)
            .build();
        doReturn(ListPostedTransactionsWithFolioResponse.newBuilder()
            .addTransactions(transactionWithFolio1)
            .addTransactions(transactionWithFolio2)
            .build())
            .when(accountingService)
            .getTransactionsByIds(
                eq(CB_PROPERTY_ID),
                eq(List.of(1L, 2L)),
                any(),
                eq(20)
            );


        var guestResponse = PersonVersion.newBuilder()
            .setId(13245634L)
            .setContactDetails(
                ContactDetails.newBuilder()
                    .setFirstName("John")
                    .setLastName("Doe")
                    .build()
            ).build();
        doReturn(Map.of(customerId, guestResponse)).when(fiscalDocumentRecipientService)
            .getGuestInfo(eq(CB_PROPERTY_ID), anySet());


        var response = getRequest(
            "/fiscal-documents/" + 3 + "/transactions",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<FiscalDocumentTransactionsPaginated>() {},
            Map.of("includeLinkedDocumentTransactions", "true")
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(2, response.getBody().getTransactions().size());

        this.validateFiscalDocumentTransactions(transactionWithFolio1, response.getBody().getTransactions().get(0));
        this.validateFiscalDocumentTransactions(transactionWithFolio2, response.getBody().getTransactions().get(1));
    }

    @ParameterizedTest
    @MethodSource("downloadScenarios")
    void testDownloadFiscalDocument(
        String fiscalDocumentId,
        HttpStatus expectedStatus
    ) {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();
        doReturn("PDF content".getBytes(StandardCharsets.UTF_8))
            .when(s3Service)
            .getContent(any(), any());
        var response = getRequest(
            "fiscal-documents/" + fiscalDocumentId + "/download",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<byte[]>() {},
            null
        );

        assertEquals(expectedStatus, response.getStatusCode());
    }

    private static Stream<Arguments> downloadScenarios() {
        return Stream.of(
            Arguments.of("1", HttpStatus.OK),
            Arguments.of("3", HttpStatus.BAD_REQUEST),
            Arguments.of("4", HttpStatus.NOT_FOUND),
            Arguments.of("9999999999", HttpStatus.NOT_FOUND),
            Arguments.of("5", HttpStatus.OK)
        );
    }

    @ParameterizedTest
    @MethodSource("emailFiscalDocumentScenarios")
    void testEmailFiscalDocument(
        String id,
        FiscalDocumentEmailRequest request,
        HttpStatus expectedResult
    ) throws IOException {
        setupDataAndMocks(DEFAULT_PROPERTY);
        fiscalDocumentPreconditions.createInvoices();

        if (HttpStatus.INTERNAL_SERVER_ERROR == expectedResult) {
            doReturn(HttpStatus.INTERNAL_SERVER_ERROR.value()).when(emailService)
                .send(any(), any(), any(), any());
        } else {
            doReturn(HttpStatus.OK.value()).when(emailService)
                .send(any(), any(), any(), any());
        }

        var response = postRequest(
            "/fiscal-documents/" + id + "/email",
            CB_PROPERTY_ID,
            request,
            Object.class
        );

        assertEquals(expectedResult, response.getStatusCode());
    }

    private void compareDocumentValue(FiscalDocumentValue expected, FiscalDocumentValue actual) {
        assertAll(
            "Errors in document " + actual.getNumber(),
            () -> assertEquals(
                expected.getPropertyId(), actual.getPropertyId(),
                "Mismatch in document's property ID"),
            () -> assertEquals(
                expected.getNumber(), actual.getNumber(),
                "Mismatch in document's number"),
            () -> assertEquals(
                expected.getInvoiceDate(), actual.getInvoiceDate(),
                "Mismatch in document's invoice date"),
                () -> assertEquals(
                expected.getDueDate(), actual.getDueDate(),
                "Mismatch in document's due date"),
            () -> assertEquals(
                expected.getKind(), actual.getKind(),
                "Mismatch in document's kind"),
            () -> assertEquals(
                expected.getUrl(), actual.getUrl(),
                "Mismatch in document's URL"),
            () -> assertEquals(
                expected.getExternalId(), actual.getExternalId(),
                "Mismatch in document's external ID"),
            () -> assertEquals(
                expected.getOrigin(), actual.getOrigin(),
                "Mismatch in document's origin"),
            () -> assertEquals(
                expected.getSourceKind(), actual.getSourceKind(),
                "Mismatch in document's source kind"),
            () -> assertEquals(
                expected.getSourceId(), actual.getSourceId(),
                "Mismatch in document's source ID"),
            () -> assertEquals(
                expected.getStatus(), actual.getStatus(),
                "Mismatch in document's status"),
            () -> assertEquals(
                expected.getAmount(), actual.getAmount(),
                "Mismatch in document's amount"),
            () -> assertEquals(
                expected.getBalance(), actual.getBalance(),
                "Mismatch in document's balance"),
            () -> assertEquals(
                expected.getCurrency(), actual.getCurrency(),
                "Mismatch in document's currency"),
            () -> assertEquals(
                expected.getTransactionIds(), actual.getTransactionIds(),
                "Mismatch in document's transaction IDs"),
            () -> assertEquals(
                expected.getLinkedDocumentIds(), actual.getLinkedDocumentIds(),
                "Mismatch in document's linked document IDs"),
            () -> assertEquals(
                expected.getRecipients(), actual.getRecipients(),
                "Mismatch in document's recipients")
        );
    }

    private static Stream<Arguments> emailFiscalDocumentScenarios() {
        FiscalDocumentEmailRequest validRequest = new FiscalDocumentEmailRequest();
        validRequest.setEmails(List.of("<EMAIL>", "<EMAIL>"));
        FiscalDocumentEmailRequest missingEmail = new FiscalDocumentEmailRequest();
        FiscalDocumentEmailRequest invalidEmail = new FiscalDocumentEmailRequest();
        invalidEmail.setEmails(List.of("invalidEmail123"));

        return Stream.of(
            Arguments.of("1", validRequest, HttpStatus.OK),
            Arguments.of("2", missingEmail, HttpStatus.BAD_REQUEST),
            Arguments.of("3", validRequest, HttpStatus.BAD_REQUEST),
            Arguments.of("999999", validRequest, HttpStatus.NOT_FOUND),
            Arguments.of("1", validRequest, HttpStatus.INTERNAL_SERVER_ERROR),
            Arguments.of("1", invalidEmail, HttpStatus.BAD_REQUEST)
        );
    }

    @Test
    void getFiscalDocumentRecipientsById() {
        setupDataAndMocks(DEFAULT_PROPERTY);
        var recipient = new com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentRecipient();
        recipient.setId("123");
        recipient.setFirstName("John");
        recipient.setLastName("Doe");
        recipient.setEmail("<EMAIL>");
        recipient.setType(com.cloudbeds.fiscaldocument.controller.model.RecipientType.PERSON);

        var address = new com.cloudbeds.fiscaldocument.controller.model.RecipientAddress();
        address.setAddress1("123 Main St");
        address.setAddress2("Apt 4B");
        address.setCity("Springfield");
        address.setState("IL");
        address.setZipCode("62704");
        address.setCountry("US");
        recipient.setAddress(address);

        var tax = new com.cloudbeds.fiscaldocument.controller.model.RecipientTaxInfo();
        tax.setId("TAX-*********");
        recipient.setTax(tax);

        var contactDetails = new com.cloudbeds.fiscaldocument.controller.model.RecipientContactDetails();
        contactDetails.setPhone("555-1234");
        contactDetails.setCellPhone("555-5678");
        contactDetails.setGender("MALE");
        contactDetails.setBirthday(OffsetDateTime.parse("1990-01-01T00:00:00Z"));
        recipient.setContactDetails(contactDetails);

        var document = new com.cloudbeds.fiscaldocument.controller.model.RecipientDocument();
        document.setType("PASSPORT");
        document.setNumber("*********");
        document.setIssuingCountry("US");
        document.setIssueDate(OffsetDateTime.parse("2010-01-01T00:00:00Z"));
        document.setExpirationDate(OffsetDateTime.parse("2030-01-01T00:00:00Z"));
        recipient.setDocument(document);

        recipient.setCountryData(Map.of(
            "US", Map.of("customField", "customValue")
        ));

        doReturn(List.of(recipient, recipient))
            .when(fiscalDocumentRecipientService)
            .findRecipientsByFiscalDocumentId(anyLong());

        var response = getRequest(
            "/fiscal-documents/1/recipients",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<
                List<com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentRecipient>
                >() {},
            null
        );

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(2, response.getBody().size());

        var responseRecipient = response.getBody().get(0);
        assertEquals("123", responseRecipient.getId());
        assertEquals("John", responseRecipient.getFirstName());
        assertEquals("Doe", responseRecipient.getLastName());
        assertEquals("<EMAIL>", responseRecipient.getEmail());
        assertEquals("PERSON", responseRecipient.getType().toString());

        assertEquals("123 Main St", responseRecipient.getAddress().getAddress1());
        assertEquals("US", responseRecipient.getAddress().getCountry());
        assertEquals("TAX-*********", responseRecipient.getTax().getId());

        assertEquals(
            "MALE",
            responseRecipient.getContactDetails().getGender()
        );
        assertEquals("PASSPORT", responseRecipient.getDocument().getType());
        assertEquals(
            "customValue",
            ((Map<String, Object>) responseRecipient.getCountryData().get("US")).get("customField")
        );
    }

    public Stream<Arguments> createFiscalDocumentSource() {
        return Stream.of(
            Arguments.of(
                "Create: default",
                DEFAULT_PROPERTY,
                false,
                "integration/create-document/invoice/default/event_key.json",
                "integration/create-document/invoice/default/event_value.json",
                "integration/create-document/invoice/default/document_value.json"
            ),
            Arguments.of(
                "Create: Spain with disabled integration",
                SPAIN_PROPERTY,
                false,
                "integration/create-document/invoice/spain/event_key.json",
                "integration/create-document/invoice/spain/event_value.json",
                "integration/create-document/invoice/spain/document_value.json"
            ),
            Arguments.of(
                "Create: Spain with enabled integration",
                SPAIN_PROPERTY,
                true,
                "integration/create-document/invoice/spain-integration/event_key.json",
                "integration/create-document/invoice/spain-integration/event_value.json",
                "integration/create-document/invoice/spain-integration/document_value.json"
            ));
    }

    public Stream<Arguments> putFiscalDocumentArguments() {
        return Stream.of(
            Arguments.of(
                "Success",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.COMPLETED_INTEGRATION,
                HttpStatus.OK,
                null,
                "integration/update-document-integration/invoice/event_key.json",
                "integration/update-document-integration/invoice/event_value.json"
            ),
            Arguments.of(
                "failed integration",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.FAILED_INTEGRATION,
                HttpStatus.OK,
                "failReason",
                "integration/update-document-integration/invoice/event_key.json",
                "integration/update-document-integration/invoice/event_value.json"
            ),
            Arguments.of(
                "Marked as failed",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.FAILED,
                HttpStatus.OK,
                "failReason",
                null,
                null
            ),
            Arguments.of(
                "Marked as failed without reason",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.FAILED,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Marked as failed integration without reason",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.FAILED_INTEGRATION,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Marked as failed integration without reason",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.FAILED_INTEGRATION,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Marked as completed without number",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.COMPLETED_INTEGRATION,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Marked as completed without url",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .officialId("1")
                    .number("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.COMPLETED_INTEGRATION,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Marked as completed without qr information",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .officialId("1")
                    .externalId("1")
                    .number("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com")),
                FiscalDocumentStatus.COMPLETED_INTEGRATION,
                HttpStatus.BAD_REQUEST,
                null,
                null,
                null
            ),
            Arguments.of(
                "Update government object without changing status",
                new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration()
                    .series("1")
                    .number("1")
                    .officialId("1")
                    .externalId("1")
                    .rectifyingInvoiceType("1")
                    .status("status")
                    .url(java.net.URI.create("https://pdf-example.com"))
                    .qr(new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr()
                        .url(java.net.URI.create("https://qr-example.com"))
                        .string("QR String")
                    ),
                FiscalDocumentStatus.PENDING_INTEGRATION,
                HttpStatus.OK,
                null,
                null,
                null
            )
        );
    }
}
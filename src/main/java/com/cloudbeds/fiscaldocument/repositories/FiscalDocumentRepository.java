package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FiscalDocumentRepository extends JpaRepository<FiscalDocument, Long>,
    JpaSpecificationExecutor<FiscalDocument> {

    Optional<FiscalDocument> findByPropertyIdAndId(long propertyId, long fiscalDocumentId);

    List<FiscalDocument> findByIdIn(Collection<Long> ids);

    /**
     * Checks if an invoice has been rectified by looking for rectifying invoices that link to it.
     * Uses optimized query with proper indexes for performance.
     *
     * @param invoiceId the ID of the invoice to check
     * @param activeStatuses list of active statuses to filter by
     * @return true if the invoice has been rectified, false otherwise
     */
    @Query("""
        SELECT CASE WHEN COUNT(fd) > 0 THEN true ELSE false END
        FROM FiscalDocument fd
        JOIN fd.linkedDocuments ld
        WHERE ld.linkedDocumentId = :invoiceId
        AND fd.kind = 'RECTIFY_INVOICE'
        AND fd.status IN :activeStatuses
        """)
    boolean hasBeenRectified(@Param("invoiceId") Long invoiceId,
                           @Param("activeStatuses") List<DocumentStatus> activeStatuses);

    /**
     * Finds all rectifying invoices for a given original invoice, ordered by creation date (newest first).
     * Uses optimized query with proper indexes for performance.
     *
     * @param originalInvoiceId the ID of the original invoice
     * @param activeStatuses list of active statuses to filter by
     * @return list of rectifying invoices
     */
    @Query("""
        SELECT fd FROM FiscalDocument fd
        JOIN fd.linkedDocuments ld
        WHERE ld.linkedDocumentId = :originalInvoiceId
        AND fd.kind = 'RECTIFY_INVOICE'
        AND fd.status IN :activeStatuses
        ORDER BY fd.createdAt DESC
        """)
    List<FiscalDocument> findRectifyingInvoicesForInvoice(@Param("originalInvoiceId") Long originalInvoiceId,
                                                         @Param("activeStatuses") List<DocumentStatus> activeStatuses);
}

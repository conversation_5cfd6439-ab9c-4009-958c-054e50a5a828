package com.cloudbeds.fiscaldocument.listeners.config;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import static com.cloudbeds.fiscaldocument.support.consumers.ConsumerUtils.getDefaultConcurrentBatchListenerFactory;
import static io.confluent.kafka.serializers.KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.AUTO_OFFSET_RESET_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.MAX_POLL_RECORDS_CONFIG;

@EnableKafka
@Configuration
@RequiredArgsConstructor
public class DualWritesConsumerConfig {
    private final KafkaProperties kafkaProperties;

    @Value("${consumer.default.backoff.ms}")
    Long backOffMs;

    @Value("${consumer.dual_writes.poll_records}")
    private String maxPollRecords;

    @Bean
    public ConcurrentKafkaListenerContainerFactory<FiscalDocumentKey, FiscalDocumentValue>
        dualWritesListenerContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(dualWritesConsumerFactory(), 1, backOffMs);
    }

    private Map<String, Object> dualWritesConsumerConfig() {
        var prop = kafkaProperties.buildConsumerProperties(null);
        prop.put(SPECIFIC_AVRO_READER_CONFIG, true);
        prop.put(MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        prop.put(AUTO_OFFSET_RESET_CONFIG, "earliest");
        return prop;
    }

    private ConsumerFactory<FiscalDocumentKey, FiscalDocumentValue> dualWritesConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(dualWritesConsumerConfig());
    }
}

package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.converters.RecipientConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.guest.GuestServiceClient;
import com.cloudbeds.fiscaldocument.grpc.guestrequirement.GuestRequirementServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRecipientRepository;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.group.v1.ContactDetail;
import com.cloudbeds.group.v1.GroupProfile;
import com.cloudbeds.group.v1.GroupProfileContact;
import com.cloudbeds.guest.v1.PersonVersion;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import static com.cloudbeds.fiscaldocument.converters.RecipientConverter.toRecipientType;

@Service
@RequiredArgsConstructor
@Slf4j
public class FiscalDocumentRecipientService {
    private final PropertyServiceClient propertyServiceClient;
    private final GuestRequirementServiceClient guestRequirementServiceClient;
    private final GuestServiceClient guestServiceClient;
    private final FiscalDocumentRecipientRepository fiscalDocumentRecipientRepository;
    private final RecipientConverter recipientConverter;
    private final GroupProfileServiceClient groupProfileServiceClient;


    /**
     * Gets fiscal document paginated.
     *
     * @param id fiscal document id
     * @return the fiscal document paginated
     */
    @Transactional(readOnly = true)
    public List<com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentRecipient>
        findRecipientsByFiscalDocumentId(long id) {
        return fiscalDocumentRecipientRepository.findAllByFiscalDocumentId(id).stream().map(
            recipient -> recipientConverter.toRest(recipient.getRecipient())
        ).collect(Collectors.toList());
    }


    /**
     * Gets all the recipients data from a CDC event.
     *
     * @param reservation map of the reservation data
     * @param groupProfile map of the group profile data
     * @return the stored message
     */
    public List<Recipient> getRecipientsFromCdc(
        Map<String, Object> reservation,
        Map<String, Object> groupProfile
    ) {
        List<Recipient> recipients = new ArrayList<>();

        if (reservation != null && !reservation.isEmpty()) {
            Recipient recipient = new Recipient();

            recipient.setId((String) reservation.get("customer_id"));
            String companyName = (String) reservation.get("company_name");
            String companyTaxId = (String) reservation.get("company_tax_id_number");

            if (StringUtils.hasText(companyName)) {
                recipient.setType(Recipient.RecipientType.COMPANY);
                recipient.setFirstName(companyName);

                Recipient.TaxInfo taxInfo = new Recipient.TaxInfo();
                taxInfo.setId(companyTaxId != null ? companyTaxId : "");
                taxInfo.setCompanyName(companyName);
                recipient.setTax(taxInfo);
            } else {
                recipient.setType(Recipient.RecipientType.PERSON);
                recipient.setFirstName((String) reservation.getOrDefault("first_name", ""));
                recipient.setLastName((String) reservation.getOrDefault("last_name", ""));
                String guestTaxId = (String) reservation.get("guest_tax_id_number");
                String cpf = (String) reservation.get("cpf");

                Recipient.TaxInfo taxInfo = new Recipient.TaxInfo();
                taxInfo.setId(
                    StringUtils.hasText(guestTaxId) ? guestTaxId :
                        (StringUtils.hasText(cpf) ? cpf : "")
                );
                recipient.setTax(taxInfo);
            }

            var address = new Recipient.Address();
            address.setAddress1((String) reservation.get("street"));
            address.setAddress2((String) reservation.get("number"));
            address.setCity((String) reservation.get("city"));
            address.setState((String) reservation.get("state"));
            address.setZipCode((String) reservation.get("zip"));
            address.setCountry((String) reservation.get("country"));
            recipient.setAddress(address);
            recipients.add(recipient);
        }

        if (groupProfile != null && !groupProfile.isEmpty()) {
            Recipient recipient = new Recipient();
            recipient.setId(String.valueOf(groupProfile.get("id")));
            recipient.setType(Recipient.RecipientType.COMPANY);

            String name = (String) groupProfile.get("name");
            recipient.setFirstName(StringUtils.hasText(name) ? name : "Group Profile # " + recipient.getId());

            Recipient.Address address = new Recipient.Address();
            address.setAddress1((String) groupProfile.get("address1"));
            address.setAddress2((String) groupProfile.get("address2"));
            address.setCity((String) groupProfile.get("city"));
            address.setState((String) groupProfile.get("state"));
            address.setZipCode((String) groupProfile.get("zip"));
            address.setCountry((String) groupProfile.get("countryCode"));
            recipient.setAddress(address);
            recipients.add(recipient);
        }

        return recipients;
    }


    /**
     * Gets all the recipients' data from a guest.
     *
     * @param propertyId       propertyId
     * @param recipientIds     recipientIds
     * @return map of recipientIds and PersonVersion
     */
    public Map<Long, PersonVersion> getGuestInfo(
        long propertyId,
        Collection<Long> recipientIds
    ) {
        var organizationId = propertyServiceClient.getProperty(propertyId).getOrganization().getId();

        return recipientIds.stream()
            .map(recipientId -> Pair.of(
                    recipientId,
                    guestServiceClient.getGuestById(organizationId, recipientId).getCurrentVersion()))
            .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
    }

    /**
     * Validates if the RecipientType applies to the SourceKind.
     *
     * @param sourceKind Source kind (e.g., RESERVATION, GROUP_PROFILE)
     * @param recipient  Recipient request containing the type
     */
    public void checkRecipientType(SourceKind sourceKind, RecipientRequest recipient) {
        var type = recipient.getType();

        boolean valid = switch (type) {
            case GROUP, CONTACT -> sourceKind == SourceKind.GROUP_PROFILE;
            case GUEST, COMPANY -> sourceKind == SourceKind.RESERVATION;
        };

        if (!valid) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                String.format(
                    "Unsupported combination: recipient type '%s' is not allowed with source kind '%s'.",
                    type, sourceKind
                )
            );
        }
    }

    /**
     * Gets all the recipients data from a guest.
     *
     * @param fiscalDocumentId id of the fiscal document
     * @param propertyId propertyId
     * @param recipients recipients
     * @param sourceKind source kind
     * @return the stored message
     */
    public List<FiscalDocumentRecipient> getRecipients(
        long fiscalDocumentId,
        long propertyId,
        List<RecipientRequest> recipients,
        SourceKind sourceKind,
        long sourceId
    ) {
        var organizationId = propertyServiceClient.getProperty(propertyId).getOrganization().getId();

        return recipients.stream()
            .map(recipient -> buildRecipient(
                    fiscalDocumentId,
                    propertyId,
                    organizationId,
                    recipient.getId(),
                    toRecipientType(recipient.getType()),
                    sourceKind,
                    sourceId
                )
            )
            .toList();
    }

    private FiscalDocumentRecipient buildRecipient(
        long fiscalDocumentId,
        long propertyId,
        long organizationId,
        long recipientId,
        Recipient.RecipientType recipientType,
        SourceKind sourceKind,
        long sourceId
    ) {
        return switch (sourceKind) {
            case RESERVATION -> buildFromReservation(
                fiscalDocumentId, propertyId, organizationId, recipientId, recipientType
            );
            case GROUP_PROFILE -> buildFromGroupProfile(
                fiscalDocumentId, propertyId, recipientId, recipientType, sourceId
            );
            case HOUSE_ACCOUNT -> throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST, "Is not possible to create Invoice for House Account"
            );
            case ACCOUNTS_RECEIVABLE_LEDGER -> throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST, "Is not possible to create Invoice for AR Ledger"
            );
        };
    }

    private FiscalDocumentRecipient buildFromReservation(
        long fiscalDocumentId,
        long propertyId,
        long organizationId,
        long recipientId,
        Recipient.RecipientType recipientType
    ) {
        var guestData = guestServiceClient.getGuestById(organizationId, recipientId).getCurrentVersion();
        var recipient = buildRecipientFromGuest(guestData, recipientId, recipientType);
        addGuestRequirements(recipient, propertyId, recipientId);

        return createFiscalDocumentRecipient(fiscalDocumentId, recipientId, recipient);
    }

    private FiscalDocumentRecipient buildFromGroupProfile(
        long fiscalDocumentId,
        long propertyId,
        long recipientId,
        Recipient.RecipientType recipientType,
        long groupProfileId
    ) {
        Recipient recipient = switch (recipientType) {
            case PERSON -> {
                var contact = groupProfileServiceClient.getGroupProfileContactById(recipientId);
                yield buildRecipientFromContactGroup(contact);
            }
            case COMPANY -> {
                var group = groupProfileServiceClient.listGroups(propertyId, List.of(groupProfileId)).getFirst();
                yield buildRecipientFromGroup(group, recipientId);
            }
        };

        return createFiscalDocumentRecipient(fiscalDocumentId, recipientId, recipient);
    }

    private FiscalDocumentRecipient createFiscalDocumentRecipient(
        long fiscalDocumentId,
        long recipientId,
        Recipient recipient
    ) {
        var docRecipient = new FiscalDocumentRecipient();
        docRecipient.setRecipientId(recipientId);
        docRecipient.setFiscalDocumentId(fiscalDocumentId);
        docRecipient.setRecipient(recipient);
        return docRecipient;
    }

    private Recipient buildRecipientFromContactGroup(GroupProfileContact contact) {
        var recipient = new Recipient();

        recipient.setId(String.valueOf(contact.getId()));
        recipient.setFirstName(contact.getFirstName());
        recipient.setLastName(contact.getLastName());
        recipient.setEmail(contact.getEmails(0).getValue());
        recipient.setEmail(
            Optional.ofNullable(getContactDetailByType(contact.getEmailsList(), "business"))
                .orElse(getContactDetailByType(contact.getEmailsList(), "personal"))
        );
        recipient.setType(Recipient.RecipientType.PERSON);

        // Address
        var address = new Recipient.Address();
        address.setCountry(null);
        address.setCity(null);
        address.setState(null);
        address.setAddress1(null);
        address.setAddress2(null);
        address.setZipCode(null);
        recipient.setAddress(address);

        // Tax Info
        var tax = new Recipient.TaxInfo();
        tax.setCompanyName(contact.getFirstName());
        tax.setId(null);
        recipient.setTax(tax);

        // Contact Details
        var contactDetails = new Recipient.ContactDetails();
        contactDetails.setPhone(
            Optional.ofNullable(
                getContactDetailByType(contact.getPhonesList(), "work"))
                .orElse(getContactDetailByType(contact.getPhonesList(), "home"))
        );
        contactDetails.setCellPhone(getContactDetailByType(
            contact.getEmailsList(),
            "cell_phone")
        );
        contactDetails.setGender(null);
        recipient.setContactDetails(contactDetails);

        // Document
        var document = new Recipient.Document();
        recipient.setDocument(document);

        return recipient;
    }

    private String getContactDetailByType(
        List<ContactDetail> contactDetails,
        String desiredType
    ) {
        return contactDetails.stream()
            .filter(phone -> phone.getType().equalsIgnoreCase(desiredType))
            .findFirst()
            .map(ContactDetail::getValue)
            .orElse(null);
    }

    private Recipient buildRecipientFromGroup(
        GroupProfile group,
        long groupProfileId
    ) {
        var recipient = new Recipient();
        recipient.setId(String.valueOf(groupProfileId));
        recipient.setFirstName(group.getName());
        recipient.setLastName("");
        recipient.setEmail("");
        recipient.setType(Recipient.RecipientType.COMPANY);

        var address = new Recipient.Address();
        address.setCountry(group.getCountryCode());
        address.setCity(group.getCity());
        address.setState(group.getState());
        address.setAddress1(group.getAddress1());
        address.setAddress2(group.getAddress2());
        address.setZipCode(group.getZip());
        recipient.setAddress(address);

        var tax = new Recipient.TaxInfo();
        tax.setCompanyName(group.getName());
        tax.setId(null);
        recipient.setTax(tax);

        var contactDetails = new Recipient.ContactDetails();
        contactDetails.setPhone(null);
        contactDetails.setGender(null);
        contactDetails.setCellPhone(null);
        recipient.setContactDetails(contactDetails);

        var document = new Recipient.Document();
        recipient.setDocument(document);

        return recipient;
    }


    private Recipient buildRecipientFromGuest(
        PersonVersion guestData,
        long guestId,
        Recipient.RecipientType recipientType
    ) {
        var contact = guestData.getContactDetails();

        var recipient = new Recipient();
        recipient.setId(String.valueOf(guestId));
        recipient.setFirstName(contact.getFirstName());
        recipient.setLastName(contact.getLastName());
        recipient.setEmail(contact.getEmail());
        recipient.setType(recipientType);

        var addressData = guestData.getAddress();
        var address = new Recipient.Address();
        address.setCountry(addressData.getCountry());
        address.setCity(addressData.getCity());
        address.setState(addressData.getState());
        address.setAddress1(addressData.getAddress1());
        address.setAddress2(addressData.getAddress2());
        address.setZipCode(addressData.getZip());
        recipient.setAddress(address);

        var taxInfo = guestData.getTaxInfo();
        var tax = new Recipient.TaxInfo();
        tax.setCompanyName(taxInfo.getCompanyName());
        tax.setId(recipient.getType() == Recipient.RecipientType.PERSON
            ? taxInfo.getGuestTaxIdNumber()
            : taxInfo.getCompanyTaxIdNumber());
        recipient.setTax(tax);

        var contactDetails = new Recipient.ContactDetails();
        contactDetails.setPhone(contact.getPhone());
        contactDetails.setGender(contact.getGender().name());
        contactDetails.setCellPhone(contact.getCellPhone());
        if (contact.hasBirthday()) {
            contactDetails.setBirthday(Instant.ofEpochSecond(contact.getBirthday().getSeconds()));
        }
        recipient.setContactDetails(contactDetails);

        var documentData = guestData.getDocument();
        var document = new Recipient.Document();
        document.setType(documentData.getType().name());
        if (documentData.hasNumber()) {
            document.setNumber(documentData.getNumber());
        }
        if (documentData.hasIssuingCountry()) {
            document.setIssuingCountry(documentData.getIssuingCountry());
        }
        if (documentData.hasIssueDate()) {
            document.setIssueDate(Instant.ofEpochSecond(documentData.getIssueDate().getSeconds()));
        }
        if (documentData.hasExpirationDate()) {
            document.setExpirationDate(Instant.ofEpochSecond(documentData.getExpirationDate().getSeconds()));
        }
        recipient.setDocument(document);

        return recipient;
    }


    private void addGuestRequirements(Recipient recipient, long propertyId, long recipientId) {
        var guestRequirements = guestRequirementServiceClient.listGuestsRequirement(propertyId, recipientId);
        guestRequirements.getFieldsList().stream()
            .filter(field -> StringUtils.hasText(field.getName()))
            .forEach(field -> recipient.setCountryData(field.getName(), field.getValue()));
    }
}
